@echo off
echo 开始构建 DL引擎编辑器...

echo.
echo 1. 检查 TypeScript 编译...
call npx tsc --noEmit
if %errorlevel% neq 0 (
    echo TypeScript 编译检查失败！
    pause
    exit /b %errorlevel%
)
echo TypeScript 编译检查通过

echo.
echo 2. 运行 Vite 构建...
call npx vite build
if %errorlevel% neq 0 (
    echo Vite 构建失败！

    pause
    exit /b %errorlevel%
)

echo.
echo 3. 修复枚举问题...
call node fix-js-enum.cjs
if %errorlevel% neq 0 (
    echo 枚举修复失败！
    pause
    exit /b %errorlevel%
)

echo.
echo 构建成功！
echo 输出目录: dist/
pause
